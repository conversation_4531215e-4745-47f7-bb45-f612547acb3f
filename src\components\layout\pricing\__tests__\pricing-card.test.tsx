import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import PricingCard from '../pricing-card';

describe('PricingCard', () => {
  const mockFeatures = ['Feature 1', 'Feature 2', 'Feature 3'];

  it('renders regular pricing card correctly', () => {
    render(
      <PricingCard
        title="Test Plan"
        price={100}
        period="month"
        features={mockFeatures}
      />
    );
    
    expect(screen.getByText('Test Plan')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('/month')).toBeInTheDocument();
    expect(screen.getByText('Get')).toBeInTheDocument();
  });

  it('renders custom pricing card with slider', () => {
    render(
      <PricingCard
        title="Custom Plan"
        features={['Lorem ipsum', 'Lorem ipsum', 'Lorem ipsum', 'Lorem ipsum']}
        isCustom={true}
      />
    );
    
    expect(screen.getByText('Custom Plan')).toBeInTheDocument();
    expect(screen.getByRole('slider')).toBeInTheDocument();
    expect(screen.getByText('/365 Days')).toBeInTheDocument();
    expect(screen.getByText('1,000')).toBeInTheDocument(); // Default calculated price
  });

  it('updates custom plan price when slider changes', () => {
    render(
      <PricingCard
        title="Custom Plan"
        features={['Lorem ipsum']}
        isCustom={true}
      />
    );
    
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: '100' } });
    
    // Price should be calculated as (100/365) * 1000 = ~274
    expect(screen.getByText('274')).toBeInTheDocument();
    expect(screen.getByText('/100 Days')).toBeInTheDocument();
  });

  it('calls onGetPlan with correct parameters for custom plan', () => {
    const mockOnGetPlan = vi.fn();
    render(
      <PricingCard
        title="Custom Plan"
        features={['Lorem ipsum']}
        isCustom={true}
        onGetPlan={mockOnGetPlan}
      />
    );
    
    const getButton = screen.getByText('Get');
    fireEvent.click(getButton);
    
    expect(mockOnGetPlan).toHaveBeenCalledWith(365, 1000);
  });

  it('calls onGetPlan without parameters for regular plan', () => {
    const mockOnGetPlan = vi.fn();
    render(
      <PricingCard
        title="Regular Plan"
        price={100}
        period="month"
        features={mockFeatures}
        onGetPlan={mockOnGetPlan}
      />
    );
    
    const getButton = screen.getByText('Get');
    fireEvent.click(getButton);
    
    expect(mockOnGetPlan).toHaveBeenCalledWith();
  });

  it('applies custom gradient styling for custom plans', () => {
    const { container } = render(
      <PricingCard
        title="Custom Plan"
        features={['Lorem ipsum']}
        isCustom={true}
      />
    );
    
    const card = container.querySelector('[style*="linear-gradient"]');
    expect(card).toBeInTheDocument();
    expect(card).toHaveStyle({
      background: 'linear-gradient(180deg, #5936CD 0%, #2D1B67 100%)'
    });
  });

  it('applies primary styling correctly', () => {
    render(
      <PricingCard
        title="Primary Plan"
        price={200}
        period="month"
        features={mockFeatures}
        isPrimary={true}
      />
    );
    
    const card = screen.getByText('Primary Plan').closest('.bg-accent');
    expect(card).toBeInTheDocument();
  });
});
