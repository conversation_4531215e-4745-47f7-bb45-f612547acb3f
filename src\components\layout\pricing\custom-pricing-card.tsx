import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	CardHeader,
} from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Check } from "react-feather";

type CustomPricingCardProps = {
	onGetPlan?: (days: number, price: number) => void;
};

const CUSTOM_PLAN_FEATURES = [
	"Lorem ipsum",
	"Lorem ipsum", 
	"Lorem ipsum",
	"Lorem ipsum",
];

const CustomPricingCard = ({
	onGetPlan = () => {},
}: CustomPricingCardProps) => {
	const [days, setDays] = useState(365);
	
	// Calculate price based on days (you can adjust this formula)
	const calculatePrice = (days: number) => {
		// Example pricing: base price of 1000 for 365 days, scaled proportionally
		return Math.round((days / 365) * 1000);
	};

	const price = calculatePrice(days);

	return (
		<Card
			className="rounded-xl w-full md:w-80 shadow-lg text-white flex flex-col"
			style={{
				background: "linear-gradient(180deg, #5936CD 0%, #2D1B67 100%)",
			}}
		>
			<CardHeader className="pb-2">
				<h3 className="font-bold">Custom Plan</h3>
				
				{/* Slider */}
				<div className="py-4">
					<Slider
						value={days}
						onChange={setDays}
						min={1}
						max={365}
						step={1}
						className="bg-white/30 [&::-webkit-slider-track]:bg-white/30 [&::-webkit-slider-track]:rounded-lg [&::-moz-range-track]:bg-white/30 [&::-moz-range-track]:rounded-lg [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-white"
					/>
				</div>

				<div className="flex items-end gap-1">
					<span className="text-xl font-bold">Rs.</span>
					<span className="text-5xl font-bold">{price.toLocaleString()}</span>
					<span className="text-base text-gray-300">
						/{days} Days
					</span>
				</div>
			</CardHeader>

			<CardContent className="space-y-6 pt-6 flex-grow">
				{CUSTOM_PLAN_FEATURES.map((feature, index) => (
					<div key={index} className="flex items-center gap-3">
						<div className="p-1">
							<Check className="h-4 w-4 text-white" />
						</div>
						<span className="text-sm">{feature}</span>
					</div>
				))}
			</CardContent>

			<CardFooter className="mt-auto pb-6">
				<Button
					onClick={() => onGetPlan(days, price)}
					className="w-full py-6 bg-white hover:bg-gray-100 text-black"
				>
					Get
				</Button>
			</CardFooter>
		</Card>
	);
};

export default CustomPricingCard;
