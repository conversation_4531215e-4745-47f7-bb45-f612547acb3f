import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON>ontent,
	<PERSON><PERSON>ooter,
	CardHeader,
} from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Check } from "react-feather";

type PricingCardProps = {
	title: string;
	price?: number; // Optional for custom plans
	period?: string; // Optional for custom plans
	features: string[];
	isPrimary?: boolean;
	isCustom?: boolean; // New prop to indicate custom plan
	onGetPlan?: (days?: number, price?: number) => void; // Updated to support custom plan params
};

const PricingCard = ({
	title,
	price,
	period,
	features,
	isPrimary = false,
	isCustom = false,
	onGetPlan = () => {},
}: PricingCardProps) => {
	// State for custom plan slider
	const [days, setDays] = useState(365);

	// Calculate price for custom plans
	const calculateCustomPrice = (days: number) => {
		return Math.round((days / 365) * 1000);
	};

	// Get the actual price and period to display
	const displayPrice = isCustom ? calculateCustomPrice(days) : price;
	const displayPeriod = isCustom ? `${days} Days` : period;

	// Handle button click
	const handleGetPlan = () => {
		if (isCustom) {
			onGetPlan(days, displayPrice);
		} else {
			onGetPlan();
		}
	};
	return (
		<Card
			className={`rounded-xl w-full md:w-80 shadow-lg ${
				isCustom
					? "text-white"
					: isPrimary
						? "bg-accent text-white"
						: "bg-white"
			} flex flex-col`}
			style={
				isCustom
					? { background: "linear-gradient(180deg, #5936CD 0%, #2D1B67 100%)" }
					: undefined
			}
		>
			<CardHeader className="pb-2">
				<h3 className="font-bold">{title}</h3>

				{/* Slider for custom plans */}
				{isCustom && (
					<div className="py-4">
						<Slider
							value={days}
							onChange={setDays}
							min={1}
							max={365}
							step={1}
							className="bg-[#D9D9D9] [&::-webkit-slider-track]:bg-[#D9D9D9] [&::-webkit-slider-track]:rounded-lg [&::-moz-range-track]:bg-[#D9D9D9] [&::-moz-range-track]:rounded-lg [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:border-2 [&::-webkit-slider-thumb]:border-white [&::-moz-range-thumb]:bg-white [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-white"
							// style={{ boxShadow: "-4px 4px 30px 0px #0000000A" }}
						/>
					</div>
				)}

				<div className="flex items-end gap-1">
					<span className="text-xl font-bold">Rs.</span>
					<span className="text-5xl font-bold">
						{displayPrice?.toLocaleString()}
					</span>
					<span
						className={`text-base ${
							isCustom || isPrimary ? "text-[#FFFFFF66]" : "text-[#656565]"
						}`}
					>
						/{displayPeriod}
					</span>
				</div>
			</CardHeader>

			<CardContent className="space-y-6 pt-6 flex-grow">
				{features.map((feature, index) => (
					<div key={index} className="flex items-center gap-3">
						<div className="p-1">
							<Check
								className={`h-4 w-4 ${
									isCustom || isPrimary ? "text-white" : "text-purple-600"
								}`}
							/>
						</div>
						<span className="text-sm">{feature}</span>
					</div>
				))}
			</CardContent>

			<CardFooter className="mt-auto pb-6">
				<Button
					onClick={handleGetPlan}
					className={`w-full py-6 ${
						isCustom || isPrimary
							? "bg-white hover:bg-gray-100 text-black"
							: "bg-accent text-white"
					}`}
				>
					Get
				</Button>
			</CardFooter>
		</Card>
	);
};

export default PricingCard;
