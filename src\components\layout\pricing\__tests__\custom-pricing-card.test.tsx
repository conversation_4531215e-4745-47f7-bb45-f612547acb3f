import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import CustomPricingCard from '../custom-pricing-card';

describe('CustomPricingCard', () => {
  it('renders with default values', () => {
    render(<CustomPricingCard />);
    
    expect(screen.getByText('Custom Plan')).toBeInTheDocument();
    expect(screen.getByText('Rs.')).toBeInTheDocument();
    expect(screen.getByText('/365 Days')).toBeInTheDocument();
    expect(screen.getByText('Get')).toBeInTheDocument();
  });

  it('displays Lorem ipsum features', () => {
    render(<CustomPricingCard />);
    
    const features = screen.getAllByText('Lorem ipsum');
    expect(features).toHaveLength(4);
  });

  it('calls onGetPlan with correct values when Get button is clicked', () => {
    const mockOnGetPlan = vi.fn();
    render(<CustomPricingCard onGetPlan={mockOnGetPlan} />);
    
    const getButton = screen.getByText('Get');
    fireEvent.click(getButton);
    
    expect(mockOnGetPlan).toHaveBeenCalledWith(365, 1000);
  });

  it('updates price when slider value changes', () => {
    render(<CustomPricingCard />);
    
    const slider = screen.getByRole('slider');
    fireEvent.change(slider, { target: { value: '100' } });
    
    // Price should be calculated as (100/365) * 1000 = ~274
    expect(screen.getByText('274')).toBeInTheDocument();
    expect(screen.getByText('/100 Days')).toBeInTheDocument();
  });

  it('has proper gradient background styling', () => {
    const { container } = render(<CustomPricingCard />);
    
    const card = container.querySelector('[style*="linear-gradient"]');
    expect(card).toBeInTheDocument();
    expect(card).toHaveStyle({
      background: 'linear-gradient(180deg, #5936CD 0%, #2D1B67 100%)'
    });
  });
});
