import React from 'react';
import { TrendingUp, TrendingDown, Icon } from 'react-feather';

export type ColorVariant = 'green' | 'red' | 'blue';

export interface AnalyticsCardProps {
  heading: string;
  number: string | number;
  icon: Icon | React.ComponentType<{ size?: number; className?: string }>;
  helperText: string;
  colorVariant: ColorVariant;
  trendDirection?: 'up' | 'down';
  trendPercentage?: string;
  className?: string;
}

const colorConfig = {
  green: {
    background: 'bg-gradient-to-r from-[rgba(116,206,206,0.15)] to-[rgba(7,112,112,0.15)]',
    iconColor: 'text-[#51A3A3]',
    trendColor: 'text-[#04F06A]'
  },
  red: {
    background: 'bg-gradient-to-r from-[rgba(243,61,85,0.15)] to-[rgba(255,5,38,0.15)]',
    iconColor: 'text-[#D7263D]',
    trendColor: 'text-[#D7263D]'
  },
  blue: {
    background: 'bg-gradient-to-r from-[rgba(134,98,255,0.15)] to-[rgba(70,41,166,0.15)]',
    iconColor: 'text-[#5936CD]',
    trendColor: 'text-[#5936CD]'
  }
};

export const AnalyticsCard: React.FC<AnalyticsCardProps> = ({
  heading,
  number,
  icon: Icon,
  helperText,
  colorVariant,
  trendDirection,
  trendPercentage,
  className = ''
}) => {
  const config = colorConfig[colorVariant];
  const TrendIcon = trendDirection === 'up' ? TrendingUp : TrendingDown;
  const trendColorClass = trendDirection === 'up' ? 'text-[#04F06A]' : 'text-[#D7263D]';

  return (
    <div className={`rounded-[14px] p-6 ${config.background} ${className}`}>
      <div className="flex justify-between items-start">
        <div>
          <p className="text-[#475569] opacity-70 font-semibold mb-4">
            {heading}
          </p>
          <p className="text-[28px] font-bold text-[#1A1C1E]">
            {number}
          </p>
        </div>
        <div className="w-[60px] h-[60px] rounded-[23px] bg-white flex items-center justify-center relative">
          <Icon size={28} className={config.iconColor} />
        </div>
      </div>
      <div className="flex items-center mt-6">
        {trendDirection && trendPercentage && (
          <>
            <TrendIcon className={`h-5 w-5 ${trendColorClass} mr-2`} />
            <p className="text-gray-700 font-semibold">
              <span className={trendColorClass}>{trendPercentage}</span> {helperText}
            </p>
          </>
        )}
        {!trendDirection && (
          <p className="text-gray-700 font-semibold">
            {helperText}
          </p>
        )}
      </div>
    </div>
  );
};

export default AnalyticsCard;
